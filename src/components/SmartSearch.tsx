// src/components/SmartSearch.js
import React, { useState, useEffect } from "react";
import { data } from "../data";
import SearchBar from "./SearchBar";
import Suggestions from "./Suggestions";
import FilterDropdown from "./FilterDropdown";
import InvoiceList from "./InvoiceList";

const SmartSearch = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilters, setActiveFilters] = useState([]);
  const [filteredInvoices, setFilteredInvoices] = useState(data.invoiceData);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);

  // Filtering logic will go here
  // Inside SmartSearch component
  useEffect(() => {
    let invoices = data.invoiceData;

    // Apply active filters
    activeFilters.forEach((filter) => {
      invoices = invoices.filter((invoice) =>
        invoice[filter.id].toLowerCase().includes(filter.value.toLowerCase())
      );
    });

    // Apply search query to searchable fields
    if (searchQuery) {
      const searchableFields = data.filterConfig
        .filter((field) => field.searchable)
        .map((field) => field.id);

      invoices = invoices.filter((invoice) =>
        searchableFields.some((field) =>
          invoice[field].toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    setFilteredInvoices(invoices);
  }, [searchQuery, activeFilters]);

  return (
    <div>
      <SearchBar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        activeFilters={activeFilters}
        // ... other props
      />
      {showSuggestions && <Suggestions /* ... props */ />}
      {showFilterDropdown && <FilterDropdown /* ... props */ />}
      <InvoiceList invoices={filteredInvoices} />
    </div>
  );
};

export default SmartSearch;
