interface Invoice {
  id: number;
  invoiceNumber: string;
  partner: string;
  salesperson: string;
  status: "Draft" | "Sent" | "Paid" | "Overdue" | "Cancelled";
  invoiceType: "Standard" | "Recurring" | "Credit Note";
  salesTeam: "Team Alpha" | "Team Beta" | "Team Gamma";
  amount: number;
  dueDate: string;
  // Index signature to allow dynamic property access
  [key: string]: any;
}

interface FilterConfigItem {
  id: string;
  label: string;
  type: "select" | "autocomplete";
  options?: string[];
  searchable?: boolean;
}

interface ActiveFilter {
  id: string;
  value: string;
  label: string;
}

interface Suggestion {
  label: string;
  filter: ActiveFilter;
}

interface FilterChipProps {
  filter: ActiveFilter;
  onRemove: (filter: ActiveFilter) => void;
}

interface InvoiceListProps {
  invoices: Invoice[];
}

export type {
  Invoice,
  FilterConfigItem,
  ActiveFilter,
  Suggestion,
  FilterChipProps,
  InvoiceListProps,
};
