import React, { useState, useEffect, useRef, FC } from "react";

// --- TYPE DEFINITIONS ---
interface Invoice {
  id: number;
  invoiceNumber: string;
  partner: string;
  salesperson: string;
  status: "Draft" | "Sent" | "Paid" | "Overdue" | "Cancelled";
  invoiceType: "Standard" | "Recurring" | "Credit Note";
  salesTeam: "Team Alpha" | "Team Beta" | "Team Gamma";
  amount: number;
  dueDate: string;
  // Index signature to allow dynamic property access
  [key: string]: any;
}

interface FilterConfigItem {
  id: string;
  label: string;
  type: "select" | "autocomplete";
  options?: string[];
  searchable?: boolean;
}

interface ActiveFilter {
  id: string;
  value: string;
  label: string;
}

interface Suggestion {
  label: string;
  filter: ActiveFilter;
}

// --- DATA ---
// In a real application, this data would likely come from an API
const data: {
  filterConfig: FilterConfigItem[];
  invoiceData: Invoice[];
} = {
  filterConfig: [
    {
      id: "status",
      label: "Status",
      type: "select",
      options: ["Draft", "Sent", "Paid", "Overdue", "Cancelled"],
    },
    { id: "partner", label: "Partner", type: "autocomplete", searchable: true },
    {
      id: "salesperson",
      label: "Salesperson",
      type: "autocomplete",
      searchable: true,
    },
    {
      id: "invoiceType",
      label: "Invoice Type",
      type: "select",
      options: ["Standard", "Recurring", "Credit Note"],
    },
    {
      id: "salesTeam",
      label: "Sales Team",
      type: "select",
      options: ["Team Alpha", "Team Beta", "Team Gamma"],
    },
  ],
  invoiceData: [
    {
      id: 1,
      invoiceNumber: "INV-001",
      partner: "McEasy Corporation",
      salesperson: "John McEasy",
      status: "Draft",
      invoiceType: "Standard",
      salesTeam: "Team Alpha",
      amount: 1500.0,
      dueDate: "2025-09-15",
    },
    {
      id: 2,
      invoiceNumber: "INV-002",
      partner: "TechFlow Solutions",
      salesperson: "Sarah Johnson",
      status: "Sent",
      invoiceType: "Recurring",
      salesTeam: "Team Beta",
      amount: 2800.0,
      dueDate: "2025-09-20",
    },
    {
      id: 3,
      invoiceNumber: "INV-003",
      partner: "McEasy Enterprises",
      salesperson: "Mike McEasy",
      status: "Paid",
      invoiceType: "Standard",
      salesTeam: "Team Alpha",
      amount: 3200.0,
      dueDate: "2025-08-30",
    },
    {
      id: 4,
      invoiceNumber: "INV-004",
      partner: "Global Industries",
      salesperson: "Lisa Chen",
      status: "Overdue",
      invoiceType: "Standard",
      salesTeam: "Team Gamma",
      amount: 4500.0,
      dueDate: "2025-08-10",
    },
    {
      id: 5,
      invoiceNumber: "INV-005",
      partner: "McEasy Holdings",
      salesperson: "David McEasy",
      status: "Draft",
      invoiceType: "Credit Note",
      salesTeam: "Team Alpha",
      amount: 1200.0,
      dueDate: "2025-09-25",
    },
    {
      id: 6,
      invoiceNumber: "INV-006",
      partner: "TechFlow Solutions",
      salesperson: "Sarah Johnson",
      status: "Cancelled",
      invoiceType: "Standard",
      salesTeam: "Team Beta",
      amount: 1800.0,
      dueDate: "2025-09-18",
    },
  ],
};

// --- HELPER ICONS ---
const SearchIcon: FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5 text-gray-400"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
);

const ChevronDownIcon: FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5 text-gray-600"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 9l-7 7-7-7"
    />
  </svg>
);

const XIcon: FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-4 w-4"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M6 18L18 6M6 6l12 12"
    />
  </svg>
);

// --- REUSABLE COMPONENTS ---

/**
 * A custom hook to detect clicks outside a referenced component.
 */
function useOutsideAlerter(
  ref: React.RefObject<HTMLElement | null>,
  onOutsideClick: () => void
) {
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (ref?.current && !ref.current.contains(event.target as Node)) {
        onOutsideClick();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref, onOutsideClick]);
}

interface FilterChipProps {
  filter: ActiveFilter;
  onRemove: (filter: ActiveFilter) => void;
}

const FilterChip: FC<FilterChipProps> = ({ filter, onRemove }) => (
  <div className="flex items-center bg-blue-100 text-blue-800 text-sm font-medium mr-2 mb-2 px-3 py-1 rounded-full">
    <span className="capitalize">{filter.label}:</span>
    <span className="ml-1 font-normal">{filter.value}</span>
    <button
      onClick={() => onRemove(filter)}
      className="ml-2 text-blue-600 hover:text-blue-800"
    >
      <XIcon />
    </button>
  </div>
);

interface InvoiceListProps {
  invoices: Invoice[];
}

const InvoiceList: FC<InvoiceListProps> = ({ invoices }) => (
  <div className="mt-6 bg-white shadow-md rounded-lg overflow-hidden">
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Invoice #
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Partner
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Salesperson
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Amount
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {invoices.length > 0 ? (
            invoices.map((invoice) => (
              <tr key={invoice.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {invoice.invoiceNumber}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {invoice.partner}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {invoice.salesperson}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      invoice.status === "Paid"
                        ? "bg-green-100 text-green-800"
                        : invoice.status === "Sent"
                        ? "bg-blue-100 text-blue-800"
                        : invoice.status === "Overdue"
                        ? "bg-red-100 text-red-800"
                        : invoice.status === "Draft"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {invoice.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${invoice.amount.toFixed(2)}
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={5} className="text-center py-10 text-gray-500">
                No invoices found.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  </div>
);

// --- MAIN APP COMPONENT ---

const App: FC = () => {
  // State for search query and active filters
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([]);

  // State for filtered data results
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>(
    data.invoiceData
  );

  // State for UI visibility
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);
  const [showFilterDropdown, setShowFilterDropdown] = useState<boolean>(false);

  // State for autocomplete suggestions
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);

  // Refs for detecting outside clicks
  const searchWrapperRef = useRef<HTMLElement | null>(null);

  // Custom hook to close dropdowns when clicking outside
  useOutsideAlerter(searchWrapperRef, () => {
    setShowSuggestions(false);
    setShowFilterDropdown(false);
  });

  // --- LOGIC ---

  // Effect to filter invoices whenever activeFilters change
  useEffect(() => {
    let result = data.invoiceData;

    if (activeFilters.length > 0) {
      result = result.filter((invoice) => {
        return activeFilters.every((filter) => {
          const invoiceValue = String(invoice[filter.id] || "").toLowerCase();
          const filterValue = String(filter.value).toLowerCase();
          return invoiceValue.includes(filterValue);
        });
      });
    }

    setFilteredInvoices(result);
  }, [activeFilters]);

  // Effect to generate autocomplete suggestions based on search query
  useEffect(() => {
    if (searchQuery.length > 0) {
      const newSuggestions: Suggestion[] = [];
      const searchableFields = data.filterConfig.filter((f) => f.searchable);

      searchableFields.forEach((field) => {
        const uniqueValues = [
          ...new Set(data.invoiceData.map((i) => i[field.id])),
        ];

        uniqueValues.forEach((value) => {
          const isAlreadyFiltered = activeFilters.some(
            (f) =>
              f.id === field.id && f.value.toLowerCase() === value.toLowerCase()
          );
          if (
            value.toLowerCase().includes(searchQuery.toLowerCase()) &&
            !isAlreadyFiltered
          ) {
            newSuggestions.push({
              label: `${field.label}: ${value}`,
              filter: { id: field.id, value: value, label: field.label },
            });
          }
        });
      });
      setSuggestions(newSuggestions);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchQuery, activeFilters]);

  // --- HANDLERS ---

  const handleAddFilter = (filter: ActiveFilter): void => {
    if (
      !activeFilters.some((f) => f.id === filter.id && f.value === filter.value)
    ) {
      setActiveFilters([...activeFilters, filter]);
    }
    setSearchQuery("");
    setShowSuggestions(false);
    setShowFilterDropdown(false);
  };

  const handleRemoveFilter = (filterToRemove: ActiveFilter): void => {
    setActiveFilters(
      activeFilters.filter(
        (f) => !(f.id === filterToRemove.id && f.value === filterToRemove.value)
      )
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    setSearchQuery(e.target.value);
    setShowFilterDropdown(false);
  };

  const toggleFilterDropdown = (): void => {
    setShowFilterDropdown(!showFilterDropdown);
    setShowSuggestions(false);
  };

  // --- RENDER ---
  return (
    <div className="bg-gray-100 min-h-screen p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Smart Search Invoices
        </h1>

        <div ref={searchWrapperRef} className="relative">
          {/* Search Bar and Filter Chips */}
          <div className="flex items-center bg-white border border-gray-300 rounded-lg shadow-sm p-2 flex-wrap">
            <div className="flex items-center flex-grow min-w-[200px]">
              <SearchIcon />
              <div className="flex flex-wrap items-center ml-2">
                {activeFilters.map((filter, index) => (
                  <FilterChip
                    key={`${filter.id}-${filter.value}-${index}`}
                    filter={filter}
                    onRemove={handleRemoveFilter}
                  />
                ))}
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={handleInputChange}
                onFocus={() =>
                  searchQuery.length > 0 && setShowSuggestions(true)
                }
                placeholder="Search..."
                className="w-full text-gray-700 placeholder-gray-500 focus:outline-none ml-2"
              />
            </div>
            <button
              onClick={toggleFilterDropdown}
              className="p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <ChevronDownIcon />
            </button>
          </div>

          {/* Autocomplete Suggestions Dropdown */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
              <ul>
                {suggestions.map((suggestion, index) => (
                  <li
                    key={index}
                    onClick={() => handleAddFilter(suggestion.filter)}
                    className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                  >
                    {suggestion.label}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Filter Shortcuts Dropdown */}
          {showFilterDropdown && (
            <div className="absolute z-10 w-full md:w-auto mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              {data.filterConfig
                .filter((f) => f.type === "select")
                .map((config) => (
                  <div key={config.id}>
                    <h3 className="font-semibold text-gray-700 mb-2">
                      {config.label}
                    </h3>
                    <ul>
                      {config.options?.map((option) => (
                        <li
                          key={option}
                          onClick={() =>
                            handleAddFilter({
                              id: config.id,
                              value: option,
                              label: config.label,
                            })
                          }
                          className="px-2 py-1 cursor-pointer hover:bg-gray-100 rounded-md text-sm text-gray-600"
                        >
                          {option}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
            </div>
          )}
        </div>

        {/* Results Area */}
        <InvoiceList invoices={filteredInvoices} />
      </div>
    </div>
  );
};

export default App;
