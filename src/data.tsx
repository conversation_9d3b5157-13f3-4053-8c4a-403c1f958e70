// src/data.js
export const data = {
  filterConfig: [
    {
      id: "status",
      label: "Status",
      type: "select",
      options: ["Draft", "Sent", "Paid", "Overdue", "Cancelled"],
    },
    {
      id: "partner",
      label: "Partner",
      type: "autocomplete",
      searchable: true,
    },
    {
      id: "salesperson",
      label: "Salesperson",
      type: "autocomplete",
      searchable: true,
    },
    {
      id: "invoice-type",
      label: "Invoice Type",
      type: "select",
      options: ["Standard", "Recurring", "Credit Note"],
    },
    {
      id: "sales-team",
      label: "Sales Team",
      type: "select",
      options: ["Team Alpha", "Team Beta", "Team Gamma"],
    },
  ],
  invoiceData: [
    {
      id: 1,
      invoiceNumber: "INV-001",
      partner: "McEasy Corporation",
      salesperson: "<PERSON>",
      status: "Draft",
      invoiceType: "Standard",
      salesTeam: "Team Alpha",
      amount: 1500.0,
      dueDate: "2025-09-15",
    },
    {
      id: 2,
      invoiceNumber: "INV-002",
      partner: "TechFlow Solutions",
      salesperson: "<PERSON>",
      status: "Sent",
      invoiceType: "Recurring",
      salesTeam: "Team Beta",
      amount: 2800.0,
      dueDate: "2025-09-20",
    },
    {
      id: 3,
      invoiceNumber: "INV-003",
      partner: "McEasy Enterprises",
      salesperson: "Mike McEasy",
      status: "Paid",
      invoiceType: "Standard",
      salesTeam: "Team Alpha",
      amount: 3200.0,
      dueDate: "2025-08-30",
    },
    {
      id: 4,
      invoiceNumber: "INV-004",
      partner: "Global Industries",
      salesperson: "Lisa Chen",
      status: "Overdue",
      invoiceType: "Standard",
      salesTeam: "Team Gamma",
      amount: 4500.0,
      dueDate: "2025-08-10",
    },
    {
      id: 5,
      invoiceNumber: "INV-005",
      partner: "McEasy Holdings",
      salesperson: "David McEasy",
      status: "Draft",
      invoiceType: "Credit Note",
      salesTeam: "Team Alpha",
      amount: 1200.0,
      dueDate: "2025-09-25",
    },
  ],
};
